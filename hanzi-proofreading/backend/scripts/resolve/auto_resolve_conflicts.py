#!/usr/bin/env python3
"""
自动解决冲突脚本

功能：
1. 识别xxt和qx一致但yd不同的冲突
2. 将这些冲突标记为已解决，原因为"忽略韵典"
3. 声母校对逻辑：根据配置文件规则自动校对声母字段
4. 韵部校对逻辑：根据配置文件规则自动校对韵部字段
5. 反切字形转换：通过fanqie_mapping.json转换字形后解决冲突
6. 支持测试模式，避免大量数据错误

使用方法：
cd hanzi-proofreading/backend/scripts/resolve
python auto_resolve_conflicts.py --test  # 测试模式，只显示结果不实际更新
python auto_resolve_conflicts.py --limit 10  # 限制处理数量
python auto_resolve_conflicts.py --execute  # 实际执行更新
python auto_resolve_conflicts.py --shengmu-only  # 仅处理声母校对
python auto_resolve_conflicts.py --yunbu-only  # 仅处理韵部校对
python auto_resolve_conflicts.py --fanqie-only  # 仅处理反切字形转换
python auto_resolve_conflicts.py --fanqie-mapping /path/to/mapping.json  # 指定字形映射文件
python auto_resolve_conflicts.py --kaihe-only # 仅处理开合冲突
python auto_resolve_conflicts.py --dengdi-only # 仅处理等第冲突
"""

import sys
import os
import argparse
import json
import re
from collections import Counter
from datetime import datetime
from typing import List, Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sqlalchemy.orm import Session
from sqlalchemy import or_
from app.database import SessionLocal
from app import models, schemas, crud


class FieldConfigLoader:
    """字段配置文件加载器"""

    def __init__(self, config_path: str = None, field_type: str = 'shengmu'):
        if config_path is None:
            if field_type == 'shengmu':
                config_path = os.path.join(os.path.dirname(__file__), 'shengmu_config.json')
            elif field_type == 'yunbu':
                config_path = os.path.join(os.path.dirname(__file__), 'yunbu_config.json')
            elif field_type == 'xiaoyun':
                config_path = os.path.join(os.path.dirname(__file__), 'xiaoyun_config.json')
            elif field_type == 'kaihe':
                config_path = os.path.join(os.path.dirname(__file__), 'kaihe_config.json')
            elif field_type == 'dengdi':
                config_path = os.path.join(os.path.dirname(__file__), 'dengdi_config.json')
            elif field_type == 'shengdiao':
                config_path = os.path.join(os.path.dirname(__file__), 'shengdiao_config.json')
            else:
                raise ValueError(f"不支持的字段类型: {field_type}")
        self.config_path = config_path
        self.field_type = field_type
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"DEBUG: Loaded config for {self.field_type} from {self.config_path}. Type: {type(data)}")
            if isinstance(data, list):
                print(f"DEBUG: Data is a list. Field type: {self.field_type}. First item: {data[0] if data else 'empty'}")

        except FileNotFoundError:
            print(f"⚠️  配置文件未找到: {self.config_path}")
            return {"rules": {}, "settings": {}}
        except json.JSONDecodeError as e:
            print(f"❌ 配置文件格式错误: {e} at {self.config_path}")
            return {"rules": {}, "settings": {}}

        if (self.field_type in ['shengmu', 'yunbu', 'xiaoyun', 'kaihe', 'dengdi']) and isinstance(data, list):
            print(f"DEBUG: Loaded config for {self.field_type} from {self.config_path}. Type: {type(data)}")
            if isinstance(data, list):
                print(f"DEBUG: Data is a list. Field type: {self.field_type}. First item: {data[0] if data else 'empty'}")
            print(f"DEBUG: Loaded config for {self.field_type} from {self.config_path}. Type: {type(data)}")
            if isinstance(data, list):
                print(f"DEBUG: Data is a list. Field type: {self.field_type}. First item: {data[0] if data else 'empty'}")
            print(f"✅ 成功加载 {self.field_type} 配置文件: {self.config_path} ({len(data)} 个规则)")
            match_rules = []
            for rule in data:
                if not isinstance(rule, dict):
                    print(f"ERROR: Rule in list is not a dictionary: {rule} in {self.config_path}")
                    continue # Skip non-dictionary rules
                condition = {
                    "xxt_value": rule.get("xxt_value"),
                    "qx_value": rule.get("qx_value"),
                    "yd_value": rule.get("yd_value")
                }
                action = {
                    "merged_value": rule.get("merged_value"),
                    "resolution_note": rule.get("resolution_note", f"自动规则: qx:'{rule.get('qx_value')}', yd:'{rule.get('yd_value')}' -> '{rule.get('merged_value')}'")
                }
                description = f"qx:'{rule.get('qx_value')}', yd:'{rule.get('yd_value')}' -> '{rule.get('merged_value')}'"
                
                match_rules.append({
                    "condition": condition,
                    "action": action,
                    "description": description
                })
            
            field_name_map = {
                'shengmu': 'sheng_mu',
                'yunbu': 'yun_bu',
                'xiaoyun': 'xiao_yun',
                'kaihe': 'kai_he',
                'dengdi': 'deng_di'
            }
            field_name = field_name_map.get(self.field_type)
            if not field_name:
                print(f"ERROR: Unknown field_type in list processing: {self.field_type}")
                return {"rules": {}, "settings": {}}
            field_name = field_name_map.get(self.field_type)
            if not field_name:
                print(f"ERROR: Unknown field_type in list processing: {self.field_type}")
                return {"rules": {}, "settings": {}}

            return {
                "rules": {
                    field_name: {
                        "match_rules": match_rules
                    }
                }
            }
        
        return data

    def get_field_rules(self, field_name: str) -> List[Dict[str, Any]]:
        """获取指定字段的校对规则"""
        field_config = self.config.get("rules", {}).get(field_name, {})
        return field_config.get("match_rules", [])

    def get_setting(self, key: str, default=None):
        """获取配置设置"""
        return self.config.get("settings", {}).get(key, default)


class FieldProofreader:
    """字段校对器"""

    def __init__(self, config_loader: FieldConfigLoader):
        self.config_loader = config_loader
        self.processed_count = 0
        self.resolved_count = 0
        self.skipped_count = 0

    def find_matching_rule(self, xxt_value: str, qx_value: str, yd_value: str, field_name: str) -> Optional[Dict[str, Any]]:
        """查找匹配的校对规则"""
        rules = self.config_loader.get_field_rules(field_name)

        for rule in rules:
            condition = rule.get("condition", {})
            if (
                condition.get("xxt_value") == xxt_value
                and condition.get("qx_value") == qx_value
                and condition.get("yd_value") == yd_value
            ):
                return rule

        return None

    def can_resolve_conflict(self, conflict: models.YunshuConflictRecord, target_field: str) -> tuple[bool, Optional[Dict[str, Any]]]:
        """检查冲突是否可以通过指定字段规则解决"""
        if not conflict.xxt_value or not conflict.qx_value or not conflict.yd_value:
            return False, None

        # 检查是否为目标字段
        if conflict.field_name != target_field:
            return False, None

        # 查找匹配的规则
        rule = self.find_matching_rule(
            conflict.xxt_value,
            conflict.qx_value,
            conflict.yd_value,
            conflict.field_name
        )

        return rule is not None, rule


class ConflictAutoResolver:
    """冲突自动解决器"""

    def __init__(self, db: Session, shengmu_config_path: str = None, yunbu_config_path: str = None, fanqie_mapping_path: str = None, fanqie_full_mapping_path: str = None, xiaoyun_config_path: str = None, kaihe_config_path: str = None, dengdi_config_path: str = None):
        self.db = db
        self.processed_count = 0
        self.resolved_count = 0
        self.skipped_count = 0

        # 初始化声母校对器
        self.shengmu_config_loader = FieldConfigLoader(shengmu_config_path, 'shengmu')
        self.shengmu_proofreader = FieldProofreader(self.shengmu_config_loader)

        # 初始化韵部校对器
        self.yunbu_config_loader = FieldConfigLoader(yunbu_config_path, 'yunbu')
        self.yunbu_proofreader = FieldProofreader(self.yunbu_config_loader)

        # 初始化小韵校对器
        self.xiaoyun_config_loader = FieldConfigLoader(xiaoyun_config_path, 'xiaoyun')
        self.xiaoyun_proofreader = FieldProofreader(self.xiaoyun_config_loader)

        # 初始化开合校对器
        self.kaihe_config_loader = FieldConfigLoader(kaihe_config_path, 'kaihe')
        self.kaihe_proofreader = FieldProofreader(self.kaihe_config_loader)

        # 初始化等第校对器
        self.dengdi_config_loader = FieldConfigLoader(dengdi_config_path, 'dengdi')
        self.dengdi_proofreader = FieldProofreader(self.dengdi_config_loader)

        # 初始化声调校对器
        self.shengdiao_config_loader = FieldConfigLoader(None, 'shengdiao')
        self.shengdiao_proofreader = FieldProofreader(self.shengdiao_config_loader)

        # 初始化反切映射
        self.fanqie_full_mapping = self._load_fanqie_full_mapping(fanqie_full_mapping_path)
        self.fanqie_mapping = self._load_fanqie_mapping(fanqie_mapping_path)

    def _load_fanqie_full_mapping(self, mapping_path: str = None) -> Dict[str, str]:
        """加载反切全值映射文件"""
        if mapping_path is None:
            mapping_path = os.path.join(os.path.dirname(__file__), 'fanqie_full_mapping.json')

        try:
            with open(mapping_path, 'r', encoding='utf-8') as f:
                mapping = json.load(f)
                print(f"✅ 成功加载反切全值映射文件: {mapping_path} ({len(mapping)} 个映射)")
                return mapping
        except FileNotFoundError:
            print(f"ℹ️  反切全值映射文件未找到: {mapping_path} (可选)")
            return {}
        except json.JSONDecodeError as e:
            print(f"❌ 反切全值映射文件格式错误: {e}")
            return {}

    def _load_fanqie_mapping(self, mapping_path: str = None) -> Dict[str, str]:
        """加载反切字形映射文件"""
        if mapping_path is None:
            mapping_path = os.path.join(os.path.dirname(__file__), 'fanqie_mapping.json')

        try:
            with open(mapping_path, 'r', encoding='utf-8') as f:
                mapping = json.load(f)
                print(f"✅ 成功加载反切字形映射文件: {mapping_path} ({len(mapping)} 个映射)")
                return mapping
        except FileNotFoundError:
            print(f"⚠️  反切字形映射文件未找到: {mapping_path}")
            return {}
        except json.JSONDecodeError as e:
            print(f"❌ 反切字形映射文件格式错误: {e}")
            return {}

    def normalize_fanqie(self, fanqie: str) -> str:
        """标准化反切：如果不以"切"结尾，则补上"切"字"""
        if not fanqie:
            return fanqie

        # 如果不以"切"结尾，则补上"切"字
        if not fanqie.endswith('切'):
            normalized = fanqie + '切'
            print(f"    [标准化] '{fanqie}' -> '{normalized}'")
            return normalized

        return fanqie

    def convert_fanqie_characters(self, fanqie: str) -> str:
        """转换反切值中的字符，包括全值映射、标准化处理和字符映射"""
        if not fanqie:
            return fanqie

        current_value = fanqie

        # 1. 全值映射
        if self.fanqie_full_mapping and current_value in self.fanqie_full_mapping:
            new_value = self.fanqie_full_mapping[current_value]
            print(f"    [全值映射] '{current_value}' -> '{new_value}'")
            current_value = new_value

        # 2. 标准化处理（补"切"字）
        normalized_value = self.normalize_fanqie(current_value)
        if normalized_value != current_value:
            current_value = normalized_value

        # 3. 逐字符转换
        if self.fanqie_mapping:
            char_mapped_value = current_value
            for old_char, new_char in self.fanqie_mapping.items():
                if old_char in char_mapped_value:
                    char_mapped_value = char_mapped_value.replace(old_char, new_char)
            
            if char_mapped_value != current_value:
                print(f"    [字符映射] '{current_value}' -> '{char_mapped_value}'")
                current_value = char_mapped_value
        
        return current_value

    def normalize_yunbu(self, yunbu: str) -> str:
        """移除韵部值中的字母 (A, B, etc.)"""
        if not yunbu:
            return yunbu
        return re.sub(r'[a-zA-Z]', '', yunbu)

    def can_resolve_yunbu_conflict_by_consensus(self, conflict: models.YunshuConflictRecord) -> tuple[bool, Optional[str], Optional[str]]:
        """
        通过移除字母达成共识来检查韵部冲突是否可解决
        """
        if conflict.field_name != 'yun_bu':
            return False, None, None

        source_values = {
            'xxt': conflict.xxt_value,
            'qx': conflict.qx_value,
            'yd': conflict.yd_value
        }
        
        normalized_values = {}
        original_present_values = []

        for source, value in source_values.items():
            if value:
                normalized_values[source] = self.normalize_yunbu(value)
                original_present_values.append(value)

        if not normalized_values:
            return False, None, None

        value_counts = Counter(normalized_values.values())
        most_common_list = value_counts.most_common()

        # 只处理所有有效值转换后都一致的情况
        if len(most_common_list) == 1:
            unified_value = most_common_list[0][0]
            if len(normalized_values) == 1 and unified_value == original_present_values[0]:
                return False, None, None
            
            agreeing_sources = normalized_values.keys()
            original_agreeing_values = [source_values[s] for s in agreeing_sources]
            was_normalized = any(self.normalize_yunbu(v) != v for v in original_agreeing_values)

            source_names = '+'.join(agreeing_sources)
            if was_normalized:
                resolution_note = f"{source_names}源移除字母后统一为'{unified_value}'"
            else:
                resolution_note = f"{source_names}源一致为'{unified_value}'"
            return True, unified_value, resolution_note

        return False, None, None

    def analyze_conflict(self, conflict: models.YunshuConflictRecord) -> Dict[str, Any]:
        """
        分析单个冲突记录
        
        Args:
            conflict: 冲突记录
            
        Returns:
            分析结果字典
        """
        can_resolve_qx = conflict.xxt_value == conflict.qx_value and conflict.yd_value != conflict.xxt_value
        can_resolve_yd = conflict.xxt_value == conflict.yd_value and conflict.qx_value != conflict.xxt_value

        resolution_reason = ""
        if can_resolve_qx:
            resolution_reason = f"xxt和qx一致为'{conflict.xxt_value}'，yd为'{conflict.yd_value}'，忽略YD差异"
        elif can_resolve_yd:
            resolution_reason = f"xxt和yd一致为'{conflict.xxt_value}'，qx为'{conflict.qx_value}'，忽略QX差异"

        return {
            'id': conflict.id,
            'hanzi': conflict.hanzi,
            'unicode': conflict.unicode,
            'fan_qie': conflict.fan_qie,
            'field_name': conflict.field_name,
            'field_display_name': conflict.field_display_name,
            'xxt_value': conflict.xxt_value,
            'qx_value': conflict.qx_value,
            'yd_value': conflict.yd_value,
            'merged_value': conflict.merged_value,
            'can_auto_resolve': can_resolve_qx or can_resolve_yd,
            'resolution_reason': resolution_reason
        }
    
    def resolve_conflict(self, conflict_id: int, test_mode: bool = True, resolution_note: str = None) -> bool:
        """
        解决单个冲突

        Args:
            conflict_id: 冲突ID
            test_mode: 是否为测试模式
            resolution_note: 解决说明

        Returns:
            是否成功解决
        """
        if test_mode:
            print(f"  [测试模式] 将解决冲突ID: {conflict.id}")
            return True

        try:
            update_data = schemas.YunshuConflictRecordUpdate(
                conflict_status="resolved",
                resolution_method="自动解决",
                resolution_note=resolution_note or "xxt和qx一致，忽略韵典差异"
            )

            updated_conflict = crud.conflict_record_crud.update_conflict_status(
                self.db, conflict_id, update_data
            )

            if updated_conflict:
                self.db.commit()
                return True
            else:
                print(f"  [错误] 未找到冲突记录ID: {conflict.id}")
                return False

        except Exception as e:
            print(f"  [错误] 解决冲突ID {conflict.id} 失败: {str(e)}")
            self.db.rollback()
            return False

    def resolve_field_conflict(self, conflict: models.YunshuConflictRecord, rule: Dict[str, Any], field_name: str, field_display_name: str, test_mode: bool = True) -> bool:
        """
        解决字段冲突并更新广韵记录

        Args:
            conflict: 冲突记录
            rule: 匹配的规则
            field_name: 字段名称
            field_display_name: 字段显示名称
            test_mode: 是否为测试模式

        Returns:
            是否成功解决
        """
        if test_mode:
            print(f"  [测试模式] 将解决{field_display_name}冲突ID: {conflict.id}")
            return True

        try:
            # 获取规则中的目标值
            action = rule.get("action", {})
            merged_value = action.get("merged_value")
            resolution_note = action.get("resolution_note", f"根据{field_display_name}校对规则自动解决")

            # 更新冲突记录状态和合并值
            conflict_update_data = schemas.YunshuConflictRecordUpdate(
                conflict_status="resolved",
                resolution_method=f"{field_display_name}校对规则",
                resolution_note=resolution_note,
                merged_value=merged_value,
                merge_rule="脚本校对"
            )

            updated_conflict = crud.conflict_record_crud.update_conflict_status(
                self.db, conflict.id, conflict_update_data
            )

            if not updated_conflict:
                print(f"  [错误] 更新冲突记录失败: {conflict.id}")
                return False

            # 更新广韵记录中的对应字段
            if conflict.guangyun_id and merged_value:
                # 根据字段名称构建更新数据
                update_kwargs = {field_name: merged_value}
                guangyun_update_data = schemas.YunshuGuangyunUpdate(**update_kwargs)

                updated_guangyun = crud.yunshu_guangyun_crud.update(
                    self.db, conflict.guangyun_id, guangyun_update_data
                )

                if not updated_guangyun:
                    print(f"  [错误] 更新广韵记录失败: {conflict.guangyun_id}")
                    self.db.rollback()
                    return False

            self.db.commit()
            return True

        except Exception as e:
            print(f"  [错误] 解决{field_display_name}冲突ID {conflict.id} 失败: {str(e)}")
            self.db.rollback()
            return False

    def process_fanqie_conflicts(self, limit: int = None, test_mode: bool = True) -> Dict[str, int]:
        """
        批量处理反切冲突

        Args:
            limit: 处理数量限制
            test_mode: 是否为测试模式

        Returns:
            处理结果统计
        """
        print(f"{'='*60}")
        print(f"开始{'测试' if test_mode else '执行'}反切冲突字形转换解决")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"模式: {'测试模式' if test_mode else '执行模式'}")
        if limit:
            print(f"限制: 最多处理 {limit} 条记录")
        print(f"字形映射: {len(self.fanqie_mapping)} 个映射规则")
        print(f"{'='*60}")

        # 查找反切冲突
        conflicts = self.find_fanqie_conflicts(limit)
        print(f"\n找到 {len(conflicts)} 条反切冲突记录")

        if not conflicts:
            print("没有需要处理的反切冲突记录")
            return {'processed': 0, 'resolved': 0, 'skipped': 0}

        # 处理每个冲突
        for i, conflict in enumerate(conflicts, 1):
            self.processed_count += 1

            print(f"\n[{i}/{len(conflicts)}] 处理反切冲突:")
            print(f"  ID: {conflict.id}")
            print(f"  汉字: {conflict.hanzi} (U+{conflict.unicode})")
            print(f"  字段: {conflict.field_display_name}")
            print(f"  xxt值: {conflict.xxt_value}")
            print(f"  qx值: {conflict.qx_value}")
            print(f"  yd值: {conflict.yd_value}")
            print(f"  当前合并值: {conflict.merged_value}")

            # 检查是否可以通过字形转换解决
            can_resolve, unified_value, resolution_note = self.can_resolve_fanqie_conflict(conflict)

            if can_resolve and unified_value:
                print(f"  转换后统一值: {unified_value}")
                print(f"  解决说明: {resolution_note}")

                # 构造规则对象用于解决冲突
                rule = {
                    "description": "反切字形转换",
                    "action": {
                        "merged_value": unified_value,
                        "resolution_note": resolution_note
                    }
                }

                if self.resolve_field_conflict(conflict, rule, 'fan_qie', '反切', test_mode):
                    self.resolved_count += 1
                    print(f"  结果: {'模拟解决' if test_mode else '已解决'}")
                else:
                    self.skipped_count += 1
                    print(f"  结果: 解决失败")
            else:
                self.skipped_count += 1
                print(f"  结果: 字形转换后仍不一致，跳过")

                # 显示转换后的值用于调试
                if self.fanqie_mapping:
                    converted_xxt = self.convert_fanqie_characters(conflict.xxt_value)
                    converted_qx = self.convert_fanqie_characters(conflict.qx_value)
                    converted_yd = self.convert_fanqie_characters(conflict.yd_value)
                    print(f"    转换后 xxt: {converted_xxt}")
                    print(f"    转换后 qx: {converted_qx}")
                    print(f"    转换后 yd: {converted_yd}")

        # 输出统计结果
        print(f"\n{'='*60}")
        print(f"反切字形转换完成统计:")
        print(f"  总处理数: {self.processed_count}")
        print(f"  成功解决: {self.resolved_count}")
        print(f"  跳过数量: {self.skipped_count}")
        print(f"  成功率: {(self.resolved_count/self.processed_count*100):.1f}%" if self.processed_count > 0 else "0%")
        print(f"{'='*60}")

        return {
            'processed': self.processed_count,
            'resolved': self.resolved_count,
            'skipped': self.skipped_count
        }

    def process_xiaoyun_conflicts(self, limit: int = None, test_mode: bool = True) -> Dict[str, int]:
        """
        批量处理小韵冲突

        Args:
            limit: 处理数量限制
            test_mode: 是否为测试模式

        Returns:
            处理结果统计
        """
        print(f"{'='*60}")
        print(f"开始{'测试' if test_mode else '执行'}小韵冲突自动校对")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"模式: {'测试模式' if test_mode else '执行模式'}")
        if limit:
            print(f"限制: 最多处理 {limit} 条记录")
        print(f"{'='*60}")

        # 查找小韵冲突
        conflicts = self.find_xiaoyun_conflicts(limit)
        print(f"\n找到 {len(conflicts)} 条小韵冲突记录")

        if not conflicts:
            print("没有需要处理的小韵冲突记录")
            return {'processed': 0, 'resolved': 0, 'skipped': 0}

        # 处理每个冲突
        for i, conflict in enumerate(conflicts, 1):
            self.processed_count += 1

            print(f"\n[{i}/{len(conflicts)}] 处理小韵冲突:")
            print(f"  ID: {conflict.id}")
            print(f"  汉字: {conflict.hanzi} (U+{conflict.unicode})")
            print(f"  反切: {conflict.fan_qie}")
            print(f"  字段: {conflict.field_display_name}")
            print(f"  xxt值: {conflict.xxt_value}")
            print(f"  qx值: {conflict.qx_value}")
            print(f"  yd值: {conflict.yd_value}")
            print(f"  当前合并值: {conflict.merged_value}")

            # 检查是否可以通过小韵规则解决
            # 直接调用 find_matching_rule 以支持包含 null 值的规则
            rule = self.xiaoyun_proofreader.find_matching_rule(
                conflict.xxt_value,
                conflict.qx_value,
                conflict.yd_value,
                'xiao_yun'
            )
            can_resolve = rule is not None

            if can_resolve and rule:
                action = rule.get("action", {})
                new_merged_value = action.get("merged_value")
                resolution_note = action.get("resolution_note", "根据小韵校对规则自动解决")

                print(f"  匹配规则: {rule.get('description', '未知规则')}")
                print(f"  新合并值: {new_merged_value}")
                print(f"  解决说明: {resolution_note}")

                if self.resolve_field_conflict(conflict, rule, 'xiao_yun', '小韵', test_mode):
                    self.resolved_count += 1
                    print(f"  结果: {'模拟解决' if test_mode else '已解决'}")
                else:
                    self.skipped_count += 1
                    print(f"  结果: 解决失败")
            else:
                self.skipped_count += 1
                print(f"  结果: 未找到匹配的校对规则，跳过")

        # 输出统计结果
        print(f"\n{'='*60}")
        print(f"小韵校对完成统计:")
        print(f"  总处理数: {self.processed_count}")
        print(f"  成功解决: {self.resolved_count}")
        print(f"  跳过数量: {self.skipped_count}")
        print(f"  成功率: {(self.resolved_count/self.processed_count*100):.1f}%" if self.processed_count > 0 else "0%")
        print(f"{'='*60}")

        return {
            'processed': self.processed_count,
            'resolved': self.resolved_count,
            'skipped': self.skipped_count
        }

    def process_kaihe_conflicts(self, limit: int = None, test_mode: bool = True) -> Dict[str, int]:
        """
        批量处理开合冲突

        Args:
            limit: 处理数量限制
            test_mode: 是否为测试模式

        Returns:
            处理结果统计
        """
        print(f"{'='*60}")
        print(f"开始{'测试' if test_mode else '执行'}开合冲突自动校对")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"模式: {'测试模式' if test_mode else '执行模式'}")
        if limit:
            print(f"限制: 最多处理 {limit} 条记录")
        print(f"{'='*60}")

        # 查找开合冲突
        conflicts = self.find_kaihe_conflicts(limit)
        print(f"\n找到 {len(conflicts)} 条开合冲突记录")

        if not conflicts:
            print("没有需要处理的开合冲突记录")
            return {'processed': 0, 'resolved': 0, 'skipped': 0}

        # 处理每个冲突
        for i, conflict in enumerate(conflicts, 1):
            self.processed_count += 1

            print(f"\n[{i}/{len(conflicts)}] 处理开合冲突:")
            print(f"  ID: {conflict.id}")
            print(f"  汉字: {conflict.hanzi} (U+{conflict.unicode})")
            print(f"  反切: {conflict.fan_qie}")
            print(f"  字段: {conflict.field_display_name}")
            print(f"  xxt值: {conflict.xxt_value}")
            print(f"  qx值: {conflict.qx_value}")
            print(f"  yd值: {conflict.yd_value}")
            print(f"  当前合并值: {conflict.merged_value}")

            # 检查是否可以通过开合规则解决
            rule = self.kaihe_proofreader.find_matching_rule(
                conflict.xxt_value,
                conflict.qx_value,
                conflict.yd_value,
                'kai_he'
            )
            can_resolve = rule is not None

            if can_resolve and rule:
                action = rule.get("action", {})
                new_merged_value = action.get("merged_value")
                resolution_note = action.get("resolution_note", "根据开合校对规则自动解决")

                print(f"  匹配规则: {rule.get('description', '未知规则')}")
                print(f"  新合并值: {new_merged_value}")
                print(f"  解决说明: {resolution_note}")

                if self.resolve_field_conflict(conflict, rule, 'kai_he', '开合', test_mode):
                    self.resolved_count += 1
                    print(f"  结果: {'模拟解决' if test_mode else '已解决'}")
                else:
                    self.skipped_count += 1
                    print(f"  结果: 解决失败")
            else:
                self.skipped_count += 1
                print(f"  结果: 未找到匹配的校对规则，跳过")

        # 输出统计结果
        print(f"\n{'='*60}")
        print(f"开合校对完成统计:")
        print(f"  总处理数: {self.processed_count}")
        print(f"  成功解决: {self.resolved_count}")
        print(f"  跳过数量: {self.skipped_count}")
        print(f"  成功率: {(self.resolved_count/self.processed_count*100):.1f}%" if self.processed_count > 0 else "0%")
        print(f"{'='*60}")

        return {
            'processed': self.processed_count,
            'resolved': self.resolved_count,
            'skipped': self.skipped_count
        }

    def process_dengdi_conflicts(self, limit: int = None, test_mode: bool = True) -> Dict[str, int]:
        """
        批量处理等第冲突

        Args:
            limit: 处理数量限制
            test_mode: 是否为测试模式

        Returns:
            处理结果统计
        """
        print(f"{'='*60}")
        print(f"开始{'测试' if test_mode else '执行'}等第冲突自动校对")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"模式: {'测试模式' if test_mode else '执行模式'}")
        if limit:
            print(f"限制: 最多处理 {limit} 条记录")
        print(f"{'='*60}")

        # 查找等第冲突
        conflicts = self.find_dengdi_conflicts(limit)
        print(f"\n找到 {len(conflicts)} 条等第冲突记录")

        if not conflicts:
            print("没有需要处理的等第冲突记录")
            return {'processed': 0, 'resolved': 0, 'skipped': 0}

        # 处理每个冲突
        for i, conflict in enumerate(conflicts, 1):
            self.processed_count += 1

            print(f"\n[{i}/{len(conflicts)}] 处理等第冲突:")
            print(f"  ID: {conflict.id}")
            print(f"  汉字: {conflict.hanzi} (U+{conflict.unicode})")
            print(f"  反切: {conflict.fan_qie}")
            print(f"  字段: {conflict.field_display_name}")
            print(f"  xxt值: {conflict.xxt_value}")
            print(f"  qx值: {conflict.qx_value}")
            print(f"  yd值: {conflict.yd_value}")
            print(f"  当前合并值: {conflict.merged_value}")

            # 检查是否可以通过等第规则解决
            rule = self.dengdi_proofreader.find_matching_rule(
                conflict.xxt_value,
                conflict.qx_value,
                conflict.yd_value,
                'deng_di'
            )
            can_resolve = rule is not None

            if can_resolve and rule:
                action = rule.get("action", {})
                new_merged_value = action.get("merged_value")
                resolution_note = action.get("resolution_note", "根据等第校对规则自动解决")

                print(f"  匹配规则: {rule.get('description', '未知规则')}")
                print(f"  新合并值: {new_merged_value}")
                print(f"  解决说明: {resolution_note}")

                if self.resolve_field_conflict(conflict, rule, 'deng_di', '等第', test_mode):
                    self.resolved_count += 1
                    print(f"  结果: {'模拟解决' if test_mode else '已解决'}")
                else:
                    self.skipped_count += 1
                    print(f"  结果: 解决失败")
            else:
                self.skipped_count += 1
                print(f"  结果: 未找到匹配的校对规则，跳过")

        # 输出统计结果
        print(f"\n{'='*60}")
        print(f"等第校对完成统计:")
        print(f"  总处理数: {self.processed_count}")
        print(f"  成功解决: {self.resolved_count}")
        print(f"  跳过数量: {self.skipped_count}")
        print(f"  成功率: {(self.resolved_count/self.processed_count*100):.1f}%" if self.processed_count > 0 else "0%")
        print(f"{'='*60}")

        return {
            'processed': self.processed_count,
            'resolved': self.resolved_count,
            'skipped': self.skipped_count
        }

    def process_shengdiao_conflicts(self, limit: int = None, test_mode: bool = True) -> Dict[str, int]:
        """
        批量处理声调冲突

        Args:
            limit: 处理数量限制
            test_mode: 是否为测试模式

        Returns:
            处理结果统计
        """
        print(f"{ '='*60}")
        print(f"开始{'测试' if test_mode else '执行'}声调冲突自动解决")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"模式: {'测试模式' if test_mode else '执行模式'}")
        if limit:
            print(f"限制: 最多处理 {limit} 条记录")
        print(f"{ '='*60}")

        conflicts = self.find_shengdiao_conflicts(limit)
        print(f"\n找到 {len(conflicts)} 条声调冲突记录")

        if not conflicts:
            print("没有需要处理的声调冲突记录")
            return {'processed': 0, 'resolved': 0, 'skipped': 0}

        processed = 0
        resolved = 0
        skipped = 0

        for i, conflict in enumerate(conflicts, 1):
            processed += 1

            print(f"\n[{i}/{len(conflicts)}] 处理声调冲突:")
            print(f"  ID: {conflict.id}")
            print(f"  汉字: {conflict.hanzi} (U+{conflict.unicode})")
            print(f"  字段: {conflict.field_display_name}")
            
            print(f"  xxt值: {conflict.xxt_value}")
            print(f"  qx值: {conflict.qx_value}")
            print(f"  yd值: {conflict.yd_value}")

            # 检查是否可以通过声调规则解决
            rule = self.shengdiao_proofreader.find_matching_rule(
                conflict.xxt_value,
                conflict.qx_value,
                conflict.yd_value,
                'sheng_diao'
            )
            can_resolve = rule is not None

            if can_resolve and rule:
                action = rule.get("action", {})
                new_merged_value = action.get("merged_value")
                resolution_note = action.get("resolution_note", "根据声调校对规则自动解决")

                print(f"  匹配规则: {rule.get('description', '未知规则')}")
                print(f"  新合并值: {new_merged_value}")
                print(f"  解决说明: {resolution_note}")

                if self.resolve_field_conflict(conflict, rule, 'sheng_diao', '声调', test_mode):
                    resolved += 1
                    print(f"  结果: {'模拟解决' if test_mode else '已解决'}")
                else:
                    skipped += 1
                    print(f"  结果: 解决失败")
            else:
                skipped += 1
                print(f"  结果: 未找到匹配的校对规则，跳过")

        print(f"\n{ '='*60}")
        print(f"声调冲突解决完成统计:")
        print(f"  总处理数: {processed}")
        print(f"  成功解决: {resolved}")
        print(f"  跳过数量: {skipped}")
        print(f"  成功率: {(resolved/processed*100):.1f}%" if processed > 0 else "0%")
        print(f"{ '='*60}")

        return {'processed': processed, 'resolved': resolved, 'skipped': skipped}


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='自动解决冲突脚本')
    parser.add_argument('--test', action='store_true', help='测试模式，不实际更新数据')
    parser.add_argument('--execute', action='store_true', help='执行模式，实际更新数据')
    parser.add_argument('--limit', type=int, help='限制处理的记录数量')
    parser.add_argument('--shengmu-only', action='store_true', help='仅处理声母校对')
    parser.add_argument('--yunbu-only', action='store_true', help='仅处理韵部校对')
    parser.add_argument('--fanqie-only', action='store_true', help='仅处理反切字形转换')
    parser.add_argument('--xiaoyun-only', action='store_true', help='仅处理小韵校对')
    parser.add_argument('--kaihe-only', action='store_true', help='仅处理开合校对')
    parser.add_argument('--dengdi-only', action='store_true', help='仅处理等第校对')
    parser.add_argument('--shengdiao-only', action='store_true', help='仅处理声调冲突')
    parser.add_argument('--shengmu-config', type=str, help='声母配置文件路径')
    parser.add_argument('--yunbu-config', type=str, help='韵部配置文件路径')
    parser.add_argument('--fanqie-mapping', type=str, help='反切字形映射文件路径')
    parser.add_argument('--fanqie-full-mapping', type=str, help='反切全值映射文件路径')
    parser.add_argument('--xiaoyun-config', type=str, help='小韵配置文件路径')
    parser.add_argument('--kaihe-config', type=str, help='开合配置文件路径')
    parser.add_argument('--dengdi-config', type=str, help='等第配置文件路径')

    args = parser.parse_args()

    # 确定运行模式
    if args.execute:
        test_mode = False
        print("⚠️  执行模式：将实际更新数据库")
    else:
        test_mode = True
        print("🔍 测试模式：只显示结果，不更新数据库")

    # 创建数据库会话
    db = SessionLocal()

    try:
        # 创建冲突解决器
        resolver = ConflictAutoResolver(
            db, 
            args.shengmu_config, 
            args.yunbu_config, 
            args.fanqie_mapping,
            args.fanqie_full_mapping,
            args.xiaoyun_config,
            args.kaihe_config,
            args.dengdi_config
        )

        # 根据参数选择处理方式
        if args.shengmu_only:
            # 仅处理声母校对
            results = resolver.process_shengmu_conflicts(
                limit=args.limit,
                test_mode=test_mode
            )
            conflict_type = "声母冲突"
        elif args.yunbu_only:
            # 仅处理韵部校对
            results = resolver.process_yunbu_conflicts(
                limit=args.limit,
                test_mode=test_mode
            )
            conflict_type = "韵部冲突"
        elif args.fanqie_only:
            # 仅处理反切字形转换
            results = resolver.process_fanqie_conflicts(
                limit=args.limit,
                test_mode=test_mode
            )
            conflict_type = "反切冲突"
        elif args.xiaoyun_only:
            # 仅处理小韵校对
            results = resolver.process_xiaoyun_conflicts(
                limit=args.limit,
                test_mode=test_mode
            )
            conflict_type = "小韵冲突"
        elif args.kaihe_only:
            # 仅处理开合校对
            results = resolver.process_kaihe_conflicts(
                limit=args.limit,
                test_mode=test_mode
            )
            conflict_type = "开合冲突"
        elif args.dengdi_only:
            # 仅处理等第校对
            results = resolver.process_dengdi_conflicts(
                limit=args.limit,
                test_mode=test_mode
            )
            conflict_type = "等第冲突"
        elif args.shengdiao_only:
            # 仅处理声调冲突
            results = resolver.process_shengdiao_conflicts(
                limit=args.limit,
                test_mode=test_mode
            )
            conflict_type = "声调冲突"
        else:
            # 处理传统的xxt/qx一致冲突
            results = resolver.process_conflicts(
                limit=args.limit,
                test_mode=test_mode
            )
            conflict_type = "冲突"

        # 如果是执行模式且有成功解决的冲突，提示用户
        if not test_mode and results['resolved'] > 0:
            print(f"\n✅ 成功解决了 {results['resolved']} 个{conflict_type}")
            print("建议检查前端界面确认结果正确")

    except Exception as e:
        print(f"❌ 脚本执行失败: {str(e)}")
        db.rollback()
        return 1
    finally:
        db.close()

    return 0


if __name__ == "__main__":
    exit(main())