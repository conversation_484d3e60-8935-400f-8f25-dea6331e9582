#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
生成小韵冲突的建议解决方案
通过查询yunshu_guangyun表来判断qx_value和yd_value中哪一个是正确值
"""

import sys
import os
import json
from sqlalchemy.orm import Session

# 将项目根目录添加到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database import SessionLocal
from app import models

def generate_xiaoyun_mapping():
    """
    生成小韵转换的键值对
    """
    db: Session = SessionLocal()
    results = []
    output_file = os.path.join(os.path.dirname(__file__), 'xiaoyun_config.json')

    try:
        # 1. 获取所有未解决的"小韵"冲突
        conflicts = db.query(
            models.YunshuConflictRecord.qx_value,
            models.YunshuConflictRecord.yd_value
        ).filter(
            models.YunshuConflictRecord.conflict_status == "unresolved",
            models.YunshuConflictRecord.field_display_name == "小韵"
        ).distinct().all()

        print(f"找到 {len(conflicts)} 对独特的小韵冲突。")

        # 2. 逐条验证
        for i, (qx_value, yd_value) in enumerate(conflicts, 1):
            merged_value = None
            is_qx_correct = False
            is_yd_correct = False
            
            print(f"[{i}/{len(conflicts)}] 正在处理: qx='{qx_value}', yd='{yd_value}'")

            # 3. 主要验证 qx_value
            try:
                qx_codepoint = hex(ord(qx_value))[2:].upper()
                qx_records = db.query(models.YunshuGuangyun).filter(
                    models.YunshuGuangyun.unicode == qx_codepoint
                ).all()
                
                if any(rec.xiao_yun == qx_value for rec in qx_records):
                    is_qx_correct = True
                    merged_value = qx_value
                    print(f"  -> [主要验证] 成功: qx_value '{qx_value}' (代码点 {qx_codepoint}) 的记录中，其xiao_yun值也为'{qx_value}'。")
            except TypeError:
                print(f"  -> [主要验证] 跳过qx_value: '{qx_value}' 不是单个字符。")

            # 4. 如果主要验证qx失败，则主要验证 yd_value
            if not is_qx_correct:
                try:
                    yd_codepoint = hex(ord(yd_value))[2:].upper()
                    yd_records = db.query(models.YunshuGuangyun).filter(
                        models.YunshuGuangyun.unicode == yd_codepoint
                    ).all()
                    
                    if any(rec.xiao_yun == yd_value for rec in yd_records):
                        is_yd_correct = True
                        merged_value = yd_value
                        print(f"  -> [主要验证] 成功: yd_value '{yd_value}' (代码点 {yd_codepoint}) 的记录中，其xiao_yun值也为'{yd_value}'。")
                except TypeError:
                    print(f"  -> [主要验证] 跳过yd_value: '{yd_value}' 不是单个字符。")

            # 5. 如果主要验证都失败，则进行交叉验证
            if not is_qx_correct and not is_yd_correct:
                # 交叉验证规则1: yd_value对应的汉字，其xiaoyun值是否为qx_value
                try:
                    yd_codepoint = hex(ord(yd_value))[2:].upper()
                    yd_records = db.query(models.YunshuGuangyun).filter(
                        models.YunshuGuangyun.unicode == yd_codepoint
                    ).all()
                    
                    cross_check_1_passed = False
                    for rec in yd_records:
                        if rec.xiao_yun == qx_value:
                            cross_check_1_passed = True
                            break
                    
                    if cross_check_1_passed:
                        merged_value = yd_value # 注意：采纳的是yd_value
                        print(f"  -> [交叉验证1] 成功: yd_value '{yd_value}' 对应记录的 xiao_yun 是 '{qx_value}'。采纳 '{yd_value}'")
                    else:
                        # 交叉验证规则2: qx_value对应的汉字，其xiaoyun值是否为yd_value
                        qx_codepoint = hex(ord(qx_value))[2:].upper()
                        qx_records = db.query(models.YunshuGuangyun).filter(
                            models.YunshuGuangyun.unicode == qx_codepoint
                        ).all()
                        if any(rec.xiao_yun == yd_value for rec in qx_records):
                            merged_value = qx_value # 注意：采纳的是qx_value
                            print(f"  -> [交叉验证2] 成功: qx_value '{qx_value}' 对应记录的 xiao_yun 是 '{yd_value}'。采纳 '{qx_value}'")
                        else:
                             print(f"  -> [所有验证失败] 未找到任何匹配。")
                except TypeError:
                    print(f"  -> [交叉验证] 跳过，存在非单个字符。")

            results.append({
                "qx_value": qx_value,
                "yd_value": yd_value,
                "merged_value": merged_value
            })

        # 5. 保存到JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=4)

        print(f"\n处理完成。结果已保存到 {output_file}")
        print(f"总共生成了 {len(results)} 条映射。")

    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    generate_xiaoyun_mapping()
